<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>详情页布局优化测试</title>
    
    <!-- External Libraries -->
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <script src="https://unpkg.com/element-plus/dist/index.full.js"></script>
    
    <!-- Element Plus Styles -->
    <link rel="stylesheet" href="https://unpkg.com/element-plus/dist/index.css">
    
    <!-- Application Styles -->
    <link rel="stylesheet" href="./assets/css/dialogs.css">
    <link rel="stylesheet" href="./assets/css/element-plus.css">
    <link rel="stylesheet" href="./assets/css/responsive.css">
    
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Inter', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .test-container {
            background: white;
            padding: 30px;
            border-radius: 20px;
            margin-bottom: 30px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            max-width: 1200px;
            margin: 0 auto 30px auto;
        }
    </style>
</head>
<body>
    <div id="app">
        <div class="test-container">
            <h1>详情页布局优化测试</h1>
            <p>这个页面用于测试优化后的详情对话框布局和展示效果。</p>
            
            <el-button type="primary" @click="openDetailDialog" size="large">
                打开优化后的详情对话框
            </el-button>
        </div>
        
        <!-- 详情查看对话框 -->
        <el-dialog
            v-model="detailDialogVisible"
            title="ES记录完整数据 - 优化版本"
            width="90%"
            center
            :modal="true"
            :close-on-click-modal="true"
            :close-on-press-escape="true"
            :show-close="true"
            destroy-on-close
            :append-to-body="true">
            
            <div v-if="currentRecord" class="detail-container">
                <div class="es-record-detail">
                    <!-- 记录概要 -->
                    <div class="record-summary">
                        <div class="summary-header">
                            <h3>{{ currentRecord.title || '无标题' }}</h3>
                            <div class="summary-meta">
                                <el-tag v-if="currentRecord.id" type="info" size="small">ID: {{ currentRecord.id }}</el-tag>
                                <el-tag v-if="currentRecord.content_source" :type="getSourceTagType(currentRecord.content_source)" size="small">
                                    {{ getSourceLabel(currentRecord.content_source) }}
                                </el-tag>
                                <el-tag v-if="currentRecord.public_time" type="success" size="small">
                                    {{ currentRecord.public_time }}
                                </el-tag>
                                <el-tag v-if="currentRecord._score" type="warning" size="small">
                                    评分: {{ currentRecord._score.toFixed(2) }}
                                </el-tag>
                            </div>
                        </div>
                    </div>

                    <!-- 主要内容展示 -->
                    <div class="main-content-section">
                        <div class="section-header">
                            <h4>主要内容</h4>
                            <el-tag v-if="currentRecord.content_source" 
                                    :type="currentRecord.content_source === 'files_v2' ? 'primary' : 'success'" 
                                    size="small">
                                {{ currentRecord.content_source === 'files_v2' ? '文件内容' : '文本内容' }}
                            </el-tag>
                        </div>
                        <div class="main-content">
                            <div v-if="getMainContent(currentRecord)" class="content-display" v-html="getMainContent(currentRecord)"></div>
                            <div v-else class="no-content">
                                <div style="font-size: 48px; margin-bottom: 16px;">📄</div>
                                <div>暂无主要内容</div>
                                <div style="font-size: 12px; color: #999; margin-top: 8px;">该记录可能没有可显示的文本内容</div>
                            </div>
                        </div>
                    </div>

                    <!-- 完整ES数据展示 -->
                    <div class="full-data-section">
                        <div class="section-header">
                            <h4>完整ES数据</h4>
                            <el-button size="small" @click="toggleDataView" type="text">
                                {{ showRawData ? '显示格式化' : '显示原始JSON' }}
                            </el-button>
                        </div>

                        <!-- 格式化数据展示 -->
                        <div v-if="!showRawData" class="formatted-data">
                            <div v-for="category in getDataCategories(currentRecord)" :key="category" class="data-category">
                                <div class="category-header">
                                    <h5>{{ getCategoryIcon(category) }} {{ getCategoryTitle(category) }}</h5>
                                    <el-tag size="small" type="info">{{ getCategoryFields(currentRecord, category).length }} 项</el-tag>
                                </div>
                                <div class="data-grid">
                                    <div v-for="key in getCategoryFields(currentRecord, category)" :key="key" 
                                         class="data-item" 
                                         :class="{ 'highlight-field': isImportantField(key) }">
                                        <div class="field-name">
                                            <span class="field-icon">{{ getFieldIcon(key) }}</span>
                                            <span class="field-text">{{ key }}</span>
                                        </div>
                                        <div class="field-value">
                                            <!-- 处理不同类型的值 -->
                                            <div v-if="key === 'url' && currentRecord[key]" class="url-value">
                                                <a :href="currentRecord[key]" target="_blank" class="url-link">{{ currentRecord[key] }}</a>
                                            </div>
                                            <div v-else-if="Array.isArray(currentRecord[key])" class="array-value">
                                                <el-tag v-if="currentRecord[key].length === 0" size="small" type="info">空数组</el-tag>
                                                <div v-else>
                                                    <div v-for="(item, index) in currentRecord[key]" :key="index" class="array-item">
                                                        {{ item }}
                                                    </div>
                                                </div>
                                            </div>
                                            <div v-else-if="isLongContent(currentRecord[key])" class="long-content">
                                                <div class="content-preview">{{ String(currentRecord[key]).substring(0, 200) }}...</div>
                                                <el-button size="small" type="text" @click="showFullContent(key, currentRecord[key])">查看完整内容</el-button>
                                            </div>
                                            <div v-else-if="currentRecord[key] === null || currentRecord[key] === undefined" class="null-value">
                                                <el-tag size="small" type="info">null</el-tag>
                                            </div>
                                            <div v-else-if="currentRecord[key] === ''" class="empty-value">
                                                <el-tag size="small" type="info">空字符串</el-tag>
                                            </div>
                                            <div v-else class="normal-value">{{ currentRecord[key] }}</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 原始JSON数据展示 -->
                        <div v-else class="raw-data">
                            <pre class="json-display">{{ JSON.stringify(currentRecord, null, 2) }}</pre>
                        </div>
                    </div>
                </div>
            </div>
            
            <template #footer>
                <div class="dialog-footer">
                    <el-button @click="detailDialogVisible = false">关闭</el-button>
                    <el-button type="primary" @click="copyFullData">
                        <el-icon><CopyDocument /></el-icon>
                        复制完整数据
                    </el-button>
                    <el-button type="success" @click="copyMainContent">
                        <el-icon><Document /></el-icon>
                        复制主要内容
                    </el-button>
                </div>
            </template>
        </el-dialog>
    </div>

    <!-- 引入工具函数 -->
    <script src="./assets/js/utils.js"></script>
    
    <script>
        const { createApp } = Vue;
        const { ElMessage } = ElementPlus;

        createApp({
            data() {
                return {
                    detailDialogVisible: false,
                    showRawData: false,
                    currentRecord: {
                        id: 12345,
                        title: "测试标题 - 这是一个用于展示详情页布局优化效果的示例记录",
                        content: "这是主要内容区域的文本。这里会显示记录的主要内容，支持HTML格式和高亮显示。内容可能会很长，所以需要合适的滚动和展示方式。",
                        dynamic_content: "动态内容示例，这里可能包含更多的详细信息和格式化的文本内容。",
                        content_source: "files_v2",
                        public_time: "2024-01-15 10:30:00",
                        MonitorDate: "2024-01-15",
                        createdAt: "2024-01-15 10:30:00",
                        updatedAt: "2024-01-15 15:45:00",
                        url: "https://example.com/test-article",
                        keyword: "测试,关键词,示例",
                        matched_keywords: ["测试", "关键词", "示例"],
                        _score: 8.75,
                        isAddToQueue: true,
                        files_v2: ["文件1.txt", "文件2.pdf", "文件3.doc"],
                        category: "新闻",
                        tags: ["重要", "测试", "示例"],
                        author: "测试作者",
                        status: "已发布",
                        source: "测试来源",
                        long_text_field: "这是一个很长的文本字段，用于测试长内容的显示效果。".repeat(10),
                        empty_field: "",
                        null_field: null,
                        array_field: ["项目1", "项目2", "项目3", "项目4", "项目5"]
                    }
                };
            },
            methods: {
                openDetailDialog() {
                    this.detailDialogVisible = true;
                },
                
                toggleDataView() {
                    this.showRawData = !this.showRawData;
                },
                
                copyFullData() {
                    const jsonData = JSON.stringify(this.currentRecord, null, 2);
                    navigator.clipboard.writeText(jsonData).then(() => {
                        ElMessage.success('完整ES数据已复制到剪贴板');
                    }).catch(() => {
                        ElMessage.error('复制失败，请手动复制');
                    });
                },
                
                copyMainContent() {
                    const mainContent = this.getMainContent(this.currentRecord);
                    if (mainContent) {
                        const textContent = mainContent.replace(/<[^>]*>/g, '');
                        navigator.clipboard.writeText(textContent).then(() => {
                            ElMessage.success('主要内容已复制到剪贴板');
                        }).catch(() => {
                            ElMessage.error('复制失败，请手动复制');
                        });
                    } else {
                        ElMessage.warning('没有找到主要内容');
                    }
                },
                
                // 工具方法
                getMainContent(record) {
                    return Utils.getMainContent(record);
                },
                
                isImportantField(key) {
                    return Utils.isImportantField(key);
                },
                
                isLongContent(value) {
                    return Utils.isLongContent(value);
                },
                
                getFieldIcon(key) {
                    return Utils.getFieldIcon(key);
                },
                
                getFieldCategory(key) {
                    return Utils.getFieldCategory(key);
                },
                
                getCategoryTitle(category) {
                    return Utils.getCategoryTitle(category);
                },
                
                getCategoryIcon(category) {
                    return Utils.getCategoryIcon(category);
                },
                
                getSourceTagType(source) {
                    return Utils.getSourceTagType(source);
                },
                
                getSourceLabel(source) {
                    return Utils.getSourceLabel(source);
                },
                
                getDataCategories(record) {
                    if (!record) return [];
                    const categories = new Set();
                    Object.keys(record).forEach(key => {
                        categories.add(Utils.getFieldCategory(key));
                    });
                    const order = ['basic', 'content', 'keywords', 'time', 'links', 'other'];
                    return Array.from(categories).sort((a, b) => {
                        return order.indexOf(a) - order.indexOf(b);
                    });
                },
                
                getCategoryFields(record, category) {
                    if (!record) return [];
                    return Object.keys(record).filter(key => {
                        return Utils.getFieldCategory(key) === category;
                    }).sort();
                },
                
                showFullContent(fieldName, content) {
                    ElMessage.info(`查看完整内容: ${fieldName}`);
                }
            }
        }).use(ElementPlus).mount('#app');
    </script>
</body>
</html>
