<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>主要内容底部布局测试</title>
    
    <!-- External Libraries -->
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <script src="https://unpkg.com/element-plus/dist/index.full.js"></script>
    
    <!-- Element Plus Styles -->
    <link rel="stylesheet" href="https://unpkg.com/element-plus/dist/index.css">
    
    <!-- Application Styles -->
    <link rel="stylesheet" href="./assets/css/dialogs.css">
    <link rel="stylesheet" href="./assets/css/element-plus.css">
    
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Inter', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .test-container {
            background: white;
            padding: 30px;
            border-radius: 20px;
            margin-bottom: 30px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            max-width: 1200px;
            margin: 0 auto 30px auto;
        }
    </style>
</head>
<body>
    <div id="app">
        <div class="test-container">
            <h1>主要内容底部布局测试</h1>
            <p>测试主要内容区域移动到三个按钮上方，并且高度自适应的效果。</p>
            
            <el-button type="primary" @click="openDetailDialog" size="large">
                打开详情对话框（主要内容在底部）
            </el-button>
        </div>
        
        <!-- 详情查看对话框 -->
        <el-dialog
            v-model="detailDialogVisible"
            title="ES记录完整数据 - 主要内容底部布局"
            width="90%"
            center
            :modal="true"
            :close-on-click-modal="true"
            :close-on-press-escape="true"
            :show-close="true"
            destroy-on-close
            :append-to-body="true">
            
            <div v-if="currentRecord" class="detail-container">
                <div class="es-record-detail">
                    <!-- 记录概要 -->
                    <div class="record-summary">
                        <div class="summary-header">
                            <h3>{{ currentRecord.title || '无标题' }}</h3>
                            <div class="summary-meta">
                                <el-tag v-if="currentRecord.id" type="info" size="small">ID: {{ currentRecord.id }}</el-tag>
                                <el-tag v-if="currentRecord.content_source" :type="getSourceTagType(currentRecord.content_source)" size="small">
                                    {{ getSourceLabel(currentRecord.content_source) }}
                                </el-tag>
                                <el-tag v-if="currentRecord.public_time" type="success" size="small">
                                    {{ currentRecord.public_time }}
                                </el-tag>
                                <el-tag v-if="currentRecord._score" type="warning" size="small">
                                    评分: {{ currentRecord._score.toFixed(2) }}
                                </el-tag>
                            </div>
                        </div>
                    </div>

                    <!-- 完整ES数据展示 -->
                    <div class="full-data-section">
                        <div class="section-header">
                            <h4>完整ES数据</h4>
                            <el-button size="small" @click="toggleDataView" type="text">
                                {{ showRawData ? '显示格式化' : '显示原始JSON' }}
                            </el-button>
                        </div>

                        <!-- 格式化数据展示 -->
                        <div v-if="!showRawData" class="formatted-data">
                            <div class="data-grid">
                                <div v-for="(value, key) in getFilteredRecord(currentRecord)" :key="key" 
                                     class="data-item" 
                                     :class="{ 'highlight-field': isImportantField(key) }">
                                    <div class="field-name">
                                        <span class="field-icon">{{ getFieldIcon(key) }}</span>
                                        <span class="field-text">{{ key }}</span>
                                    </div>
                                    <div class="field-value">
                                        <!-- 处理不同类型的值 -->
                                        <div v-if="key === 'url' && value" class="url-value">
                                            <a :href="value" target="_blank" class="url-link">{{ value }}</a>
                                        </div>
                                        <div v-else-if="Array.isArray(value)" class="array-value">
                                            <el-tag v-if="value.length === 0" size="small" type="info">空数组</el-tag>
                                            <div v-else>
                                                <div v-for="(item, index) in value" :key="index" class="array-item">
                                                    {{ item }}
                                                </div>
                                            </div>
                                        </div>
                                        <div v-else-if="isLongContent(value)" class="long-content">
                                            <div class="content-preview">{{ String(value).substring(0, 200) }}...</div>
                                            <el-button size="small" type="text" @click="showFullContent(key, value)">查看完整内容</el-button>
                                        </div>
                                        <div v-else-if="value === null || value === undefined" class="null-value">
                                            <el-tag size="small" type="info">null</el-tag>
                                        </div>
                                        <div v-else-if="value === ''" class="empty-value">
                                            <el-tag size="small" type="info">空字符串</el-tag>
                                        </div>
                                        <div v-else class="normal-value">{{ value }}</div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 原始JSON数据展示 -->
                        <div v-else class="raw-data">
                            <pre class="json-display">{{ JSON.stringify(currentRecord, null, 2) }}</pre>
                        </div>
                    </div>
                    
                    <!-- 主要内容展示 - 移动到底部 -->
                    <div class="main-content-section-bottom">
                        <div class="section-header">
                            <h4>主要内容</h4>
                            <el-tag v-if="currentRecord.content_source" 
                                    :type="currentRecord.content_source === 'files_v2' ? 'primary' : 'success'" 
                                    size="small">
                                {{ currentRecord.content_source === 'files_v2' ? '文件内容' : '文本内容' }}
                            </el-tag>
                        </div>
                        <div class="main-content-bottom">
                            <!-- 优先显示高亮内容，然后是动态内容，最后是原始内容 -->
                            <div v-if="getMainContent(currentRecord)" class="content-display-bottom" v-html="getMainContent(currentRecord)"></div>
                            <div v-else class="no-content">
                                <div style="font-size: 48px; margin-bottom: 16px;">📄</div>
                                <div>暂无主要内容</div>
                                <div style="font-size: 12px; color: #999; margin-top: 8px;">该记录可能没有可显示的文本内容</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <template #footer>
                <div class="dialog-footer">
                    <el-button @click="detailDialogVisible = false">关闭</el-button>
                    <el-button type="primary" @click="copyFullData">
                        <el-icon><CopyDocument /></el-icon>
                        复制完整数据
                    </el-button>
                    <el-button type="success" @click="copyMainContent">
                        <el-icon><Document /></el-icon>
                        复制主要内容
                    </el-button>
                </div>
            </template>
        </el-dialog>
    </div>

    <!-- 引入工具函数 -->
    <script src="./assets/js/utils.js"></script>
    
    <script>
        const { createApp } = Vue;
        const { ElMessage } = ElementPlus;

        createApp({
            data() {
                return {
                    detailDialogVisible: false,
                    showRawData: false,
                    currentRecord: {
                        id: 12345,
                        title: "Silverado Summit 2025 Keynote Presentation by Maureen Hinman",
                        content: "Silverado Co-founder and Chairman, Maureen Hinman, delivered the keynote presentation at the 2025 Silverado Summit. View the Summit keynote on YouTube.",
                        dynamic_content: `Silverado Co-founder and Chairman, Maureen Hinman, delivered the keynote presentation at the 2025 Silverado Summit.

This comprehensive presentation covered key topics including:
- Strategic vision for 2025 and beyond
- Innovation in technology and business practices  
- Leadership insights and industry trends
- Future opportunities and challenges
- Collaborative partnerships and growth strategies

The keynote was well-received by attendees and provides valuable insights into the company's direction and priorities for the coming year. The presentation demonstrates Silverado's commitment to excellence and forward-thinking approach to business development.

Key highlights from the presentation include discussions on digital transformation, sustainable business practices, and the importance of maintaining strong stakeholder relationships. Maureen Hinman's leadership experience and strategic vision were evident throughout the presentation.

View the complete Summit keynote presentation on YouTube for detailed insights and comprehensive coverage of all topics discussed during this important corporate event.`,
                        content_source: "files_v2",
                        public_time: "2025-07-31 16:46:31",
                        MonitorDate: "2025-07-31",
                        createdAt: "2025-07-31 16:46:31",
                        updatedAt: "2025-07-31 16:46:31",
                        url: "https://youtube.com/watch?v=example",
                        keyword: "Silverado,Summit,2025,Keynote,Maureen Hinman",
                        matched_keywords: ["Silverado", "Summit", "2025", "Keynote", "Maureen Hinman"],
                        _score: 9.25,
                        isAddToQueue: true,
                        files_v2: ["presentation.pdf", "keynote_slides.pptx", "summit_materials.doc"],
                        category: "Business",
                        tags: ["重要", "峰会", "主题演讲"],
                        author: "Maureen Hinman",
                        status: "已发布",
                        source: "YouTube"
                    }
                };
            },
            methods: {
                openDetailDialog() {
                    this.detailDialogVisible = true;
                },
                
                toggleDataView() {
                    this.showRawData = !this.showRawData;
                },
                
                copyFullData() {
                    const jsonData = JSON.stringify(this.currentRecord, null, 2);
                    navigator.clipboard.writeText(jsonData).then(() => {
                        ElMessage.success('完整ES数据已复制到剪贴板');
                    }).catch(() => {
                        ElMessage.error('复制失败，请手动复制');
                    });
                },
                
                copyMainContent() {
                    const mainContent = this.getMainContent(this.currentRecord);
                    if (mainContent) {
                        const textContent = mainContent.replace(/<[^>]*>/g, '');
                        navigator.clipboard.writeText(textContent).then(() => {
                            ElMessage.success('主要内容已复制到剪贴板');
                        }).catch(() => {
                            ElMessage.error('复制失败，请手动复制');
                        });
                    } else {
                        ElMessage.warning('没有找到主要内容');
                    }
                },
                
                // 过滤掉主要内容字段，避免重复显示
                getFilteredRecord(record) {
                    const filtered = { ...record };
                    delete filtered.content;
                    delete filtered.dynamic_content;
                    delete filtered.text;
                    delete filtered.dynamic_content_highlighted;
                    delete filtered.content_highlighted;
                    return filtered;
                },
                
                // 工具方法
                getMainContent(record) {
                    return Utils.getMainContent(record);
                },
                
                isImportantField(key) {
                    return Utils.isImportantField(key);
                },
                
                isLongContent(value) {
                    return Utils.isLongContent(value);
                },
                
                getFieldIcon(key) {
                    return Utils.getFieldIcon(key);
                },
                
                getSourceTagType(source) {
                    return Utils.getSourceTagType(source);
                },
                
                getSourceLabel(source) {
                    return Utils.getSourceLabel(source);
                },
                
                showFullContent(fieldName, content) {
                    ElMessage.info(`查看完整内容: ${fieldName}`);
                }
            }
        }).use(ElementPlus).mount('#app');
    </script>
</body>
</html>
