/* 响应式设计 */
@media (min-width: 1400px) {
    .container {
        max-width: 98%;
    }

    .content-preview {
        max-width: 600px;
    }
}

@media (max-width: 768px) {
    body {
        padding: 10px;
    }

    .container {
        max-width: 98%;
        padding: 15px;
    }

    .form-row {
        flex-direction: column;
        gap: 15px;
    }

    .form-item {
        min-width: unset;
    }

    .content-preview {
        max-width: 200px;
    }

    .stat-card {
        padding: 20px;
    }

    .header h1 {
        font-size: 2em;
    }

    .query-form {
        padding: 20px;
    }

    .stats-cards {
        grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
        gap: 15px;
    }

    /* 移动设备对话框样式 */
    .el-dialog {
        width: 95% !important;
        max-width: 95% !important;
        margin: 0 !important;
    }

    .detail-container {
        max-height: 60vh !important;
    }

    .data-item {
        grid-template-columns: 1fr !important;
        gap: 12px !important;
        padding: 15px !important;
    }

    .field-name {
        font-size: 12px !important;
        padding: 6px 10px !important;
        margin-bottom: 8px;
    }

    .field-icon {
        font-size: 14px !important;
    }

    .category-header {
        padding: 12px 15px !important;
    }

    .category-header h5 {
        font-size: 14px !important;
    }

    .data-category {
        margin-bottom: 20px !important;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 10px;
    }

    .header h1 {
        font-size: 1.8em;
    }

    .stat-number {
        font-size: 1.5em;
    }

    .stats-cards {
        grid-template-columns: 1fr;
    }

    /* 小屏幕设备对话框优化 */
    .el-dialog {
        width: 98% !important;
        max-width: 98% !important;
        max-height: 95vh !important;
    }

    .el-dialog__body {
        max-height: calc(95vh - 100px) !important;
        padding: 10px !important;
    }

    .detail-container {
        max-height: calc(95vh - 100px) !important;
    }

    .es-record-detail {
        padding: 10px !important;
    }

    .record-summary {
        padding: 15px !important;
        margin-bottom: 15px !important;
    }

    .main-content-section {
        padding: 15px !important;
        margin-bottom: 15px !important;
    }

    .formatted-data {
        padding: 15px !important;
        max-height: calc(95vh - 200px) !important;
    }

    .data-grid {
        padding: 15px !important;
        gap: 10px !important;
    }

    .data-category {
        margin-bottom: 15px !important;
    }

    .category-header {
        padding: 10px 12px !important;
        flex-direction: column !important;
        align-items: flex-start !important;
        gap: 8px !important;
    }

    .category-header h5 {
        font-size: 13px !important;
    }

    /* 底部主要内容区域移动端优化 */
    .main-content-section-bottom {
        margin: 15px 0 0 0 !important;
    }

    .main-content-section-bottom .section-header {
        padding: 12px 15px !important;
    }

    .main-content-section-bottom .section-header h4 {
        font-size: 1.1em !important;
    }

    .main-content-bottom {
        padding: 15px !important;
        min-height: 100px !important;
    }

    .content-display-bottom {
        padding: 15px !important;
        font-size: 14px !important;
        min-height: 60px !important;
    }
}