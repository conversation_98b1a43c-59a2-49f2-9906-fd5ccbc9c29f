/* 详情对话框样式 */
.detail-container {
    max-height: 80vh;
    overflow-y: auto;
    background: #f8f9fa;
    border-radius: 12px;
    display: flex;
    flex-direction: column;
}

/* 确保对话框居中显示 */
.el-dialog {
    position: fixed !important;
    top: 50% !important;
    left: 50% !important;
    transform: translate(-50%, -50%) !important;
    margin: 0 !important;
    max-height: 90vh !important;
    overflow: hidden !important;
}

/* 对话框遮罩层确保覆盖整个视口 */
.el-overlay {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    width: 100vw !important;
    height: 100vh !important;
    z-index: 2000 !important;
}

/* 对话框内容区域滚动 */
.el-dialog__body {
    max-height: calc(90vh - 120px) !important;
    overflow-y: auto !important;
}

.es-record-detail {
    padding: 0;
    display: flex;
    flex-direction: column;
    gap: 20px;
    flex: 1;
    min-height: 0;
}

.record-summary {
    margin: 0;
    padding: 25px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 16px;
    color: white;
    box-shadow: 0 8px 32px rgba(102, 126, 234, 0.3);
    position: relative;
    overflow: hidden;
}

.record-summary::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(255,255,255,0.1) 0%, transparent 50%);
    pointer-events: none;
}

.summary-header {
    position: relative;
    z-index: 1;
}

.summary-header h3 {
    margin: 0 0 20px 0;
    font-size: 1.6em;
    line-height: 1.3;
    font-weight: 700;
    text-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.summary-meta {
    display: flex;
    gap: 12px;
    flex-wrap: wrap;
    align-items: center;
}

.summary-meta .el-tag {
    background: rgba(255,255,255,0.2) !important;
    border: 1px solid rgba(255,255,255,0.3) !important;
    color: white !important;
    backdrop-filter: blur(10px);
    font-weight: 500;
}

.main-content-section {
    margin: 0;
    padding: 0;
    background: white;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
    border: 1px solid rgba(0,0,0,0.05);
    overflow: hidden;
}

.main-content-section .section-header {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
    padding: 20px 25px;
    border-bottom: none;
}

.main-content-section .section-header h4 {
    margin: 0;
    color: white;
    font-size: 1.3em;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 10px;
}

.main-content-section .section-header h4::before {
    content: '📄';
    font-size: 1.2em;
}

.main-content {
    background: white;
    padding: 25px;
    margin: 0;
    border-radius: 0;
    border: none;
}

.content-display {
    line-height: 1.8;
    color: #2c3e50;
    word-break: break-word;
    font-size: 15px;
    max-height: 300px;
    overflow-y: auto;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 12px;
    border-left: 4px solid #28a745;
}

.content-display::-webkit-scrollbar {
    width: 6px;
}

.content-display::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.content-display::-webkit-scrollbar-thumb {
    background: #28a745;
    border-radius: 3px;
}

.no-content {
    color: #6c757d;
    font-style: italic;
    text-align: center;
    padding: 40px 20px;
    background: #f8f9fa;
    border-radius: 12px;
    border: 2px dashed #dee2e6;
}

.full-data-section {
    background: white;
    border: 1px solid rgba(0,0,0,0.05);
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
    margin: 0;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 25px;
    background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
    border-bottom: none;
    color: white;
}

.section-header h4 {
    margin: 0;
    color: white;
    font-size: 1.3em;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 10px;
}

.section-header h4::before {
    content: '🔍';
    font-size: 1.2em;
}

.section-header .el-button {
    background: rgba(255,255,255,0.2) !important;
    border: 1px solid rgba(255,255,255,0.3) !important;
    color: white !important;
    backdrop-filter: blur(10px);
    border-radius: 8px !important;
    padding: 8px 16px !important;
    font-weight: 500;
    transition: all 0.3s ease;
}

.section-header .el-button:hover {
    background: rgba(255,255,255,0.3) !important;
    transform: translateY(-1px);
}

.formatted-data {
    padding: 25px;
    background: #f8f9fa;
}

.data-grid {
    display: grid;
    gap: 16px;
    max-height: 250px;
    overflow-y: auto;
    padding-right: 10px;
}

.data-grid::-webkit-scrollbar {
    width: 6px;
}

.data-grid::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.data-grid::-webkit-scrollbar-thumb {
    background: #6c757d;
    border-radius: 3px;
}

.data-item {
    display: grid;
    grid-template-columns: 220px 1fr;
    gap: 20px;
    padding: 18px;
    border: 1px solid rgba(0,0,0,0.08);
    border-radius: 12px;
    background: white;
    box-shadow: 0 2px 8px rgba(0,0,0,0.04);
    transition: all 0.3s ease;
    position: relative;
}

.data-item:hover {
    box-shadow: 0 4px 16px rgba(0,0,0,0.1);
    transform: translateY(-2px);
}

.data-item.highlight-field {
    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
    border-color: #f39c12;
    box-shadow: 0 4px 16px rgba(243, 156, 18, 0.2);
}

.data-item.highlight-field::before {
    content: '⭐';
    position: absolute;
    top: 12px;
    right: 12px;
    font-size: 16px;
}

.field-name {
    font-weight: 700;
    color: #2c3e50;
    word-break: break-word;
    font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
    font-size: 13px;
    background: #e9ecef;
    padding: 8px 12px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    min-height: 20px;
    gap: 8px;
}

.field-icon {
    font-size: 16px;
    flex-shrink: 0;
}

.field-text {
    flex: 1;
}

.field-value {
    color: #2c3e50;
    word-break: break-word;
    font-size: 14px;
    line-height: 1.6;
    display: flex;
    align-items: center;
    min-height: 36px;
}

.array-value {
    background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
    padding: 12px;
    border-radius: 10px;
    border: 1px solid #2196f3;
    max-height: 200px;
    overflow-y: auto;
}

.array-value::-webkit-scrollbar {
    width: 4px;
}

.array-value::-webkit-scrollbar-thumb {
    background: #2196f3;
    border-radius: 2px;
}

.array-item {
    padding: 8px 12px;
    margin: 4px 0;
    background: rgba(255,255,255,0.7);
    border-radius: 6px;
    border-left: 3px solid #2196f3;
    font-size: 13px;
}

.array-item:last-child {
    margin-bottom: 0;
}

.long-content {
    background: linear-gradient(135deg, #f1f3f4 0%, #e0e0e0 100%);
    padding: 16px;
    border-radius: 10px;
    border-left: 4px solid #4285f4;
    position: relative;
}

.long-content::before {
    content: '📄';
    position: absolute;
    top: 12px;
    right: 12px;
    font-size: 16px;
}

.content-preview {
    margin-bottom: 12px;
    line-height: 1.6;
    font-size: 13px;
    color: #555;
}

.null-value, .empty-value {
    font-style: italic;
    opacity: 0.7;
}

.url-value {
    display: flex;
    align-items: center;
    gap: 8px;
}

.url-value::before {
    content: '🔗';
    font-size: 16px;
}

.url-value a {
    color: #1976d2;
    text-decoration: none;
    word-break: break-all;
    padding: 8px 12px;
    background: #e3f2fd;
    border-radius: 8px;
    border: 1px solid #bbdefb;
    transition: all 0.3s ease;
    flex: 1;
}

.url-value a:hover {
    background: #bbdefb;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(25, 118, 210, 0.2);
}

.raw-data {
    padding: 25px;
    background: #1a1a1a;
    border-radius: 0 0 16px 16px;
}

.json-display {
    background: linear-gradient(135deg, #2d3748 0%, #1a202c 100%);
    color: #e2e8f0;
    padding: 25px;
    border-radius: 12px;
    font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
    font-size: 13px;
    line-height: 1.6;
    overflow-x: auto;
    white-space: pre-wrap;
    word-break: break-word;
    border: 1px solid #4a5568;
    box-shadow: inset 0 2px 8px rgba(0,0,0,0.3);
    max-height: 500px;
    overflow-y: auto;
    position: relative;
}

.json-display::before {
    content: 'JSON';
    position: absolute;
    top: 12px;
    right: 12px;
    background: #4a5568;
    color: #e2e8f0;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 10px;
    font-weight: bold;
}

.json-display::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

.json-display::-webkit-scrollbar-track {
    background: #1a202c;
    border-radius: 4px;
}

.json-display::-webkit-scrollbar-thumb {
    background: #4a5568;
    border-radius: 4px;
}

.json-display::-webkit-scrollbar-thumb:hover {
    background: #718096;
}

.full-content-container {
    max-height: 60vh;
    overflow-y: auto;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 6px;
}

.dialog-footer {
    text-align: right;
    padding: 20px 0 0 0;
    border-top: 1px solid #e9ecef;
    background: white;
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    align-items: center;
}

.dialog-footer .el-button {
    border-radius: 10px !important;
    padding: 12px 20px !important;
    font-weight: 600 !important;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1) !important;
}

.dialog-footer .el-button:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 4px 16px rgba(0,0,0,0.15) !important;
}

.dialog-footer .el-button--primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    border: none !important;
}

.dialog-footer .el-button--success {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%) !important;
    border: none !important;
}

/* 底部主要内容区域样式 */
.main-content-section-bottom {
    margin: 20px 0 0 0;
    padding: 0;
    background: white;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
    border: 1px solid rgba(0,0,0,0.05);
    overflow: hidden;
}

.main-content-section-bottom .section-header {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
    padding: 16px 20px;
    border-bottom: none;
}

.main-content-section-bottom .section-header h4 {
    margin: 0;
    color: white;
    font-size: 1.2em;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 10px;
}

.main-content-section-bottom .section-header h4::before {
    content: '📄';
    font-size: 1.1em;
}

.main-content-bottom {
    background: white;
    padding: 20px;
    margin: 0;
    border-radius: 0;
    border: none;
    max-height: none;
    min-height: 120px;
}

.content-display-bottom {
    line-height: 1.8;
    color: #2c3e50;
    word-break: break-word;
    font-size: 15px;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 12px;
    border-left: 4px solid #28a745;
    min-height: 80px;
    max-height: none;
    overflow: visible;
}

/* 自适应高度的内容显示 */
.content-display-bottom {
    height: auto !important;
    max-height: none !important;
    overflow: visible !important;
}

/* 确保长内容可以完全显示 */
.content-display-bottom * {
    max-height: none !important;
    overflow: visible !important;
}

/* 防止对话框被页面滚动影响 */
.el-dialog__wrapper {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    width: 100% !important;
    height: 100% !important;
    z-index: 2000 !important;
}

/* 对话框动画优化 */
.el-dialog {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

.el-dialog.el-dialog--center {
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
}

/* 确保对话框内容不会超出视口 */
.el-dialog__header {
    padding: 20px 20px 10px 20px !important;
}

.el-dialog__footer {
    padding: 10px 20px 20px 20px !important;
}

/* 滚动条样式优化 */
.detail-container::-webkit-scrollbar,
.el-dialog__body::-webkit-scrollbar {
    width: 6px;
}

.detail-container::-webkit-scrollbar-track,
.el-dialog__body::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.detail-container::-webkit-scrollbar-thumb,
.el-dialog__body::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.detail-container::-webkit-scrollbar-thumb:hover,
.el-dialog__body::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}